2025-08-19 14:48:48,707 - INFO - ✅ LLM (gemini-2.5-pro) 初始化成功。
2025-08-19 14:48:48,708 - INFO - 🧹 检测到旧的历史记录目录，正在清理 './chroma_db'...
2025-08-19 14:48:48,708 - INFO - ✅ 旧的历史记录已成功清理。
2025-08-19 14:48:49,182 - INFO - ✅ 向量化历史管理器初始化成功，数据将持久化到 './chroma_db' (遥测已禁用)。
2025-08-19 14:48:49,307 - INFO - ✅ 成功加载 3 个MCP工具
2025-08-19 14:48:49,316 - INFO - 
--- 自主智能体已准备就绪 ---
2025-08-19 14:48:57,324 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:48:57,331 - INFO - 📞 工具调用: NewTask 参数 (原始字符串): 
2025-08-19 14:48:57,331 - INFO - 📞 工具调用: None 参数: {
  "task_description": "初始化项目并判断项目框架，目标目录：C:\\Users\\<USER>\\Desktop\\dify-main"
}
2025-08-19 14:48:57,360 - INFO - NewTask called with: 初始化项目并判断项目框架，目标目录：C:\Users\<USER>\Desktop\dify-main
2025-08-19 14:48:57,361 - INFO - 
📋 [TaskStack] 添加单个任务
2025-08-19 14:48:57,361 - INFO -    详情: 任务: 初始化项目并判断项目框架，目标目录：C:\Users\<USER>\Desktop\dify-main
2025-08-19 14:48:57,361 - INFO -    📚 当前任务树 (共 1 个任务):
2025-08-19 14:48:57,361 - INFO - └── 🔄 初始化项目并判断项目框架，目标目录：C:\Users\<USER>\Desktop\dify-main (ID: 12df2f81)  <== [正在执行]
2025-08-19 14:48:57,361 - INFO - 
2025-08-19 14:48:57,362 - INFO - 🧠 正在为新任务 '初始化项目并判断项目框架，目标目录：C:\Users\<USER>\Desktop\dify-mai...' 检索历史经验...
2025-08-19 14:48:58,499 - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/embeddings "HTTP/1.1 200 OK"
2025-08-19 14:48:58,526 - INFO - 📂 未找到相关历史记录。
2025-08-19 14:49:02,379 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:49:03,244 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main"
}
2025-08-19 14:49:03,249 - INFO - 

--- 📊 Token 使用情况 ---
2025-08-19 14:49:03,249 - INFO - 输入 Tokens: 10792
2025-08-19 14:49:03,249 - INFO - 输出 Tokens: 183
2025-08-19 14:49:03,249 - INFO - 总计 Tokens: 10975
2025-08-19 14:49:03,249 - INFO - --------------------------
2025-08-19 14:49:09,812 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:49:12,379 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\package.json"
}
2025-08-19 14:49:12,379 - INFO - 

--- 📊 Token 使用情况 ---
2025-08-19 14:49:12,379 - INFO - 输入 Tokens: 11251
2025-08-19 14:49:12,379 - INFO - 输出 Tokens: 563
2025-08-19 14:49:12,379 - INFO - 总计 Tokens: 11814
2025-08-19 14:49:12,379 - INFO - --------------------------
2025-08-19 14:49:16,056 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:49:17,357 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web"
}
2025-08-19 14:49:17,365 - INFO - 

--- 📊 Token 使用情况 ---
2025-08-19 14:49:17,365 - INFO - 输入 Tokens: 11635
2025-08-19 14:49:17,366 - INFO - 输出 Tokens: 292
2025-08-19 14:49:17,366 - INFO - 总计 Tokens: 11927
2025-08-19 14:49:17,366 - INFO - --------------------------
2025-08-19 14:49:21,349 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:49:22,363 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api"
}
2025-08-19 14:49:22,458 - INFO - 

--- 📊 Token 使用情况 ---
2025-08-19 14:49:22,458 - INFO - 输入 Tokens: 12043
2025-08-19 14:49:22,459 - INFO - 输出 Tokens: 226
2025-08-19 14:49:22,459 - INFO - 总计 Tokens: 12269
2025-08-19 14:49:22,459 - INFO - --------------------------
2025-08-19 14:49:27,850 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:49:30,501 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\pyproject.toml"
}
2025-08-19 14:49:30,521 - INFO - 

--- 📊 Token 使用情况 ---
2025-08-19 14:49:30,521 - INFO - 输入 Tokens: 12381
2025-08-19 14:49:30,521 - INFO - 输出 Tokens: 399
2025-08-19 14:49:30,522 - INFO - 总计 Tokens: 12780
2025-08-19 14:49:30,522 - INFO - --------------------------
2025-08-19 14:49:41,298 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 14:49:43,742 - INFO - 
--- 流程执行完毕，正在清理所有异步资源 ---
2025-08-19 14:49:43,752 - INFO - 
--- Final Token Usage Summary ---
Model: gemini-2.5-pro
Total Prompt Tokens: 0
Total Completion Tokens: 0
Total Tokens Used: 0
---------------------------------
2025-08-19 14:49:43,752 - INFO - 
--- 所有资源已成功清理 ---
