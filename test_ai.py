import asyncio
import logging
import os
import sys

# --- LangChain & LangGraph 核心库 ---
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from tools import all_base_tools  # 从新的tools包中导入所有基础工具

base_url = "http://124.220.37.159:8301/v1"
api_key = "sk-"
model_name = "glm-4.5"

# 基础日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[

        logging.StreamHandler() # 同时输出到控制台
    ]
)

# 初始化可流式输出的聊天模型
llm = ChatOpenAI(
    model=model_name,
    openai_api_key=api_key,
    openai_api_base=base_url,
    max_retries=20,
    temperature=0.8,
    streaming=True,  # 开启底层 token 流
    timeout=360,
)

# 构建 ReAct Agent（chat_agent_executor.create_react_agent 的实现已在本地）
main_agent = create_react_agent(llm, tools=all_base_tools)

import json
from langchain_core.messages import ToolMessage


logging.info("--- Agent 开始运行 ---\n")

last_ai_response = ""
for token, metadata in main_agent.stream(
        {"messages": [{"role": "user", "content": "查看当前有哪些文件"}]},
        stream_mode="messages"
):
    # token 是一个消息块 (AIMessageChunk, ToolMessage, etc.)
    
    # 1. 处理工具调用
    # 当模型决定调用工具时，它会出现在 additional_kwargs 中
    if tool_calls := token.additional_kwargs.get("tool_calls"):
        last_ai_response = ""  # 清空之前的内容，准备捕获工具调用后的最终回复
        for tool_call in tool_calls:
            # 'function' 键包含名称和参数
            function_info = tool_call.get("function", {})
            tool_name = function_info.get("name")
            tool_args_str = function_info.get("arguments", "{}")
            
            logging.info(f"\n\n--- 📞 工具调用: {tool_name} ---")
            try:
                # 美化JSON输出
                tool_args = json.loads(tool_args_str)
                logging.info(f"参数: {json.dumps(tool_args, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError:
                logging.info(f"参数 (原始字符串): {tool_args_str}")
            logging.info("---------------------------------")
        continue 

    if isinstance(token, ToolMessage):
        logging.info(f"\n\n--- 📦 工具返回 ---")
        try:
            # content 是一个JSON字符串，里面是包含结果的列表
            tool_outputs = json.loads(token.content)
            for res in tool_outputs:
                tool_name = res.get('name')
                output_str = res.get('output', '')
                # 截断过长的输出
                if len(output_str) > 1000:
                    output_str = output_str[:1000] + "\n... (结果已截断)"
                logging.info(f"工具: {tool_name}\n结果:\n{output_str}")
        except (json.JSONDecodeError, TypeError):
            logging.info(f"原始结果: {token.content}")
        logging.info("--------------------")
        continue

    if content := token.content:
        sys.stdout.write(content)
        sys.stdout.flush()
        last_ai_response += content

    if hasattr(token, 'usage_metadata') and token.usage_metadata:
        logging.info(f"\n\n--- 📊 Token 使用情况 ---")
        logging.info(f"输入 Tokens: {token.usage_metadata.get('input_tokens')}")
        logging.info(f"输出 Tokens: {token.usage_metadata.get('output_tokens')}")
        logging.info(f"总计 Tokens: {token.usage_metadata.get('total_tokens')}")
        logging.info("--------------------------")


logging.info("\n\n--- 最后一条回复 ---")
logging.info(last_ai_response)
logging.info("--------------------")

logging.info("\n\n--- Agent 运行结束 ---")


