# main.py

import asyncio
from pyexpat import model
import traceback
import json
import re
import logging
import os
import shutil
from typing import Optional
import httpx


# --- Token Tracker ---
class TokenTracker:
    def __init__(self):
        self.total_prompt_tokens = 0
        self.total_completion_tokens = 0
        self.total_tokens = 0
        self.model_name = ""

    def set_model_name(self, model_name):
        self.model_name = model_name

    def track_tokens(self, response):
        """Tracks tokens from AIMessage or a similar object with response_metadata."""
        if hasattr(response, 'response_metadata') and 'token_usage' in response.response_metadata:
            token_usage = response.response_metadata['token_usage']
            prompt_tokens = token_usage.get('prompt_tokens', 0)
            completion_tokens = token_usage.get('completion_tokens', 0)
            
            if prompt_tokens > 0 or completion_tokens > 0:
                self.total_prompt_tokens += prompt_tokens
                self.total_completion_tokens += completion_tokens
                self.total_tokens += prompt_tokens + completion_tokens
                
                logging.info(
                    f"TOKEN_USAGE - Model: {self.model_name}, "
                    f"Prompt: {prompt_tokens}, Completion: {completion_tokens}, "
                    f"Total: {prompt_tokens + completion_tokens}"
                )

    def get_summary(self):
        return (
            f"\n--- Final Token Usage Summary ---\n"
            f"Model: {self.model_name}\n"
            f"Total Prompt Tokens: {self.total_prompt_tokens}\n"
            f"Total Completion Tokens: {self.total_completion_tokens}\n"
            f"Total Tokens Used: {self.total_tokens}\n"
            f"---------------------------------"
        )

token_tracker = TokenTracker()


# --- LangChain & LangGraph 核心库 ---
from langchain_core.messages import BaseMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, Field
from openai import OpenAI

from langchain import hub
# --- 导入我们自己的模块化组件 ---
from task_manager import Task, TaskStackManager
from tools import all_base_tools # 从新的tools包中导入所有基础工具
from prompts import SUB_AGENT_PROMPT, MAIN_AGENT_PROMPT # 导入 prompts
# 导入MCP客户端管理器
from mcp_tools_loader import load_all_mcp_tools, cleanup_mcp_sessions
from history_manager import VectorizedHistoryManager

# --- 定义全局变量 ---
app = None
all_tools = []
history_manager = None # 在全局作用域中声明
llm = None # 在全局作用域中声明


# --- 初始化LLM ---
# glm

# base_url="https://open.bigmodel.cn/api/paas/v4/"
# api_key="11019a9be0bd453ca364829f852dd5f3.btCxpeyGRvBwHd5T"
# model_name = "GLM-4.5"

# base_url="https://api.ibsgss.website/v1"
# api_key = "sk-S65Ksdt55qdqvyChsrFJDMTRbQ5IaW3iUeyh9OCQNZvcWssT"
# model_name = "claude-opus-4-20250514-thinking"

#公益站
# base_url = "https://one-hub.passerbywtj.us.kg/v1"
# api_key="sk-8XER70xCYM7hPsV_i72ILZzKKbz3p7dSwxV1dEQKi9m90hEmrgjRCdNvOsk"
# model_name = "deepseek-ai/DeepSeek-R1-0528"

#代理站
# base_url = "http://124.220.37.159:8301/v1"
# api_key = "sk-0eOt5j4XhuBkSn9mBIZigspdqkSHhyvGENO7fEFkYqvcEAfC"
# model_name = "deepseek-ai/DeepSeek-R1-0528"

#4.1-mini
# base_url = "https://tbai.xin/v1"
# api_key = "sk-LOdjI1sxJ0r53TDnl7n8D4wWrGfyVhvkfKf0nzLXwnopCWlR"
# model_name = "gpt-4.1-mini"


base_url = "http://124.220.37.159:8301/v1"
api_key = "sk-0eOt5j4XhuBkSn9mBIZigspdqkSHhyvGENO7fEFkYqvcEAfC"
model_name = "gemini-2.5-pro"

# gemini
# base_url = "http://47.96.154.128:8420/proxy/gemini-free/v1beta/openai"
# api_key = "sk-gbF41YG_vwAJ2E1YgePhIi8j-pg3SZJK7pVI5Jbi2S3ZsHOL"
# model_name = "gemini-2.5-pro"

# base_url = "http://47.96.154.128:8420/proxy/targon/v1"
# api_key = "sk-JsU3xCkS0LMNuHtzcoKhqW2t-ZY3L5PdQUNU_d_QSrEyBtZe"
# model_name = "Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8"


# --- 初始化历史管理器 ---
EMBEDDING_BASE_URL = "https://api.siliconflow.cn/v1"
EMBEDDING_API_KEY = "sk-zrpeouyilapveahhwtxoyuampujfplikgqjkauvjhztwasos"
EMBEDDING_MODEL_NAME = "BAAI/bge-m3"
CHROMA_PERSIST_DIR = "./chroma_db"  # 定义持久化目录


project_path=r"C:\Users\<USER>\Desktop\dify-main"

# 基础工具现在从tools包中导入
base_tools = all_base_tools


from task_manager import TaskStackManager, Task # 导入 TaskStackManager 和 Task

# 初始化任务管理器
task_manager = TaskStackManager()


class NewTaskInput(BaseModel):
    task_description: str = Field(description="需要解决的任务，并且要给出明确的目标")
    Additional_information: Optional[str] = Field(default=None, description="需要额外补充的知识，因为子智能体和父智能体的记忆内容不互通")
@tool(args_schema=NewTaskInput)
async def NewTask(task_description: str, Additional_information: Optional[str] = None) -> str:
    """当你遇到复杂的需要递归的子任务时，使用该工具可以帮你去解决处理子任务。请提供子任务的目标"""
    logging.info(f"NewTask called with: {task_description}")

    parent_task = task_manager.peek()
    new_task = Task(description=task_description, status="in_progress")

    if parent_task:
        task_manager.add_child_task(parent_task.task_id, new_task)
    else:
        task_manager.push(new_task)

    logging.info(f"🧠 正在为新任务 '{task_description[:50]}...' 检索历史经验...")
    relevant_history = await history_manager.search_relevant_history(task_description)
    if not relevant_history:
        logging.info("📂 未找到相关历史记录。")

    instructions = f"""
    **--- 当前任务指令 ---**
    你要严格遵守系统提示词中的要求。
    1.  **目标项目根目录**: `{project_path}`.路径内容禁止转义.
    2.  **任务继承关系**:
        {task_manager.format_task_branch_info(task_manager.get_task_branch(new_task.task_id))}
    3.  **上级传递的关键信息**: {Additional_information if Additional_information else "无"}
    4.  **你需要完成的核心任务**: "{task_description}"
    请立即开始评估你的核心任务并行动。
    5.  **有关当前任务状态的历史信息**:
    {relevant_history if relevant_history else "无相关历史记录可供参考。"}
    """

    new_agent = create_react_agent(llm, tools=all_tools)
    prompt = [
        {"role": "system", "content": SUB_AGENT_PROMPT.format(
            model_name=model_name,
            task_description=task_description
        )},
        {"role": "user", "content": instructions}
    ]

    last_ai_response = ""
    max_retries = 3
    retry_delay = 5  # seconds

    for attempt in range(max_retries):
        try:
            interaction_history = []
            async for chunk in new_agent.astream({"messages": prompt}):
                interaction_history.append(chunk)
                if "agent" in chunk and "messages" in chunk["agent"]:
                    for msg in chunk["agent"]["messages"]:
                        if isinstance(msg, AIMessage):
                            token_tracker.track_tokens(msg)
                            if msg.content:
                                logging.info(f"\n🔍 [子任务-AI思考/回应]: {msg.content}")
                                last_ai_response = "子任务执行结果：" + msg.content
                            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                                for tool_call in msg.tool_calls:
                                    logging.info(f"🛠️  [子任务-工具调用]: 调用 `{tool_call['name']}`，参数: {tool_call['args']}")
                if "tools" in chunk and "messages" in chunk["tools"]:
                    for output in chunk["tools"]["messages"]:
                        if isinstance(output, ToolMessage):
                            logging.info(f"\n✅ [子任务-工具输出]:\n{output.content}")

            await history_manager.add_history(interaction_history)
            logging.info("✅ 子任务交互已成功存入向量化历史记录。")
            task_manager.pop(status="completed")
            return last_ai_response  # Success, exit the retry loop

        except (httpx.ReadError, httpx.TimeoutException) as e:
            error_type = "网络读取错误" if isinstance(e, httpx.ReadError) else "网络请求超时"
            logging.warning(f"⚠️ {error_type} (尝试 {attempt + 1}/{max_retries}): {e}。将在 {retry_delay} 秒后重试...")
            if attempt + 1 == max_retries:
                logging.error("❌ 已达到最大重试次数，任务失败。")
                raise  # Re-raise the exception to be caught by the outer handler
            await asyncio.sleep(retry_delay)
            continue # Go to the next retry attempt

        except Exception as e:
            error_message = f"执行子任务 '{new_task.description}' 时失败: {e}"
            logging.error(f"❌ {error_message}")
            logging.error(traceback.format_exc())
            task_manager.pop(status="failed")
            replan_prompt = f"""
            **Subtask Execution Failed**
            - **Failed Task**: "{new_task.description}"
            - **Reason for Failure**: {str(e)}
            - **Parent Task Goal** (for reference): {parent_task.description if parent_task else "None"}
            **Action Instructions**:
            Primary agent, you must reevaluate the situation based on the failure information above. Please formulate a new, adjusted plan to solve or bypass this problem, or re-execute the task.
            You may choose:
            1.  **Modify and Retry**: Design a different approach to complete the original task.
            2.  **Break Down Task**: If the original task is too complex, break it down into smaller, more specific steps.
            3.  **Seek Clarification**: If the failure is due to missing information, you can ask questions to the higher level.
            **Please immediately analyze the reason for failure and output your new plan.**
            """
            return replan_prompt

    # This part is reached only if all retries fail for httpx.ReadError
    task_manager.pop(status="failed")
    return f"任务 '{new_task.description}' 因持续的网络错误而失败。"



async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("agent_log.log", mode='w', encoding="utf-8"), 
            logging.StreamHandler() # 同时输出到控制台
        ]
    )
    global app, all_tools, history_manager, llm, token_tracker



    llm = ChatOpenAI(
        model=model_name,
        openai_api_key=api_key,
        openai_api_base=base_url,
        max_retries=20,
        temperature=0.8,
        streaming=False,
        timeout=360
    )

    logging.info(f"✅ LLM ({model_name}) 初始化成功。")
    token_tracker.set_model_name(model_name)


    # --- 在初始化前，清理旧的数据库目录 ---
    if os.path.exists(CHROMA_PERSIST_DIR):
        logging.info(f"🧹 检测到旧的历史记录目录，正在清理 '{CHROMA_PERSIST_DIR}'...")
        shutil.rmtree(CHROMA_PERSIST_DIR)
        logging.info("✅ 旧的历史记录已成功清理。")

    history_manager = VectorizedHistoryManager(
        api_key=EMBEDDING_API_KEY,
        base_url=EMBEDDING_BASE_URL,
        model_name=EMBEDDING_MODEL_NAME,
        persist_directory=CHROMA_PERSIST_DIR # 传入持久化目录
    )
    
    all_tools = base_tools.copy()
    all_tools.append(NewTask)  # 添加递归规划器工具

    try:
        mcp_tools = await load_all_mcp_tools()
        if mcp_tools:
            all_tools.extend(mcp_tools)
            logging.info(f"✅ 成功加载 {len(mcp_tools)} 个MCP工具") 
        else:
            logging.info("⚠️ 没有加载到MCP工具") 
    except Exception as e:
        logging.error(f"⚠️ MCP工具加载时发生错误: {e}") # 使用logging.error
    
    # 创建执行器代理

    main_agent_tools = [tool for tool in all_base_tools if tool.name != "create_report" and tool.name != "read_file" ]
    main_agent_tools.append(NewTask)
    main_agent = create_react_agent(llm, main_agent_tools)


    # --- 主交互和执行循环 ---
    logging.info("\n--- 自主智能体已准备就绪 ---") 
    main_request = input("请输入您的总体任务目标: ")

    try:
        prompt = [
            {"role": "system", "content": MAIN_AGENT_PROMPT.format(
                project_path=project_path,
                main_request=main_request
            )},
            {"role": "user", "content": f"""审计步骤参考信息：
                         1.使用init的工具初始化目录`{project_path}`项目
                         2.判断项目框架
                         3.寻找入口点
                         4.逐个模块、逐个功能逐步的递归审计（需要考虑所有漏洞类型）
                         
<final_goal>
{main_request}
</final_goal>"""},
        ]
        # 用于存储主任务的完整交互记录
        main_interaction_history = []
        async for chunk in main_agent.astream({"messages": prompt}, {"recursion_limit": 500000}):
            main_interaction_history.append(chunk) # 记录数据块

            # --- 更健壮的日志记录逻辑 ---
            # 检查 'agent' key 并打印 AIMessage
            if 'agent' in chunk and 'messages' in chunk['agent']:
                for msg in chunk['agent']['messages']:
                    if isinstance(msg, AIMessage):
                        token_tracker.track_tokens(msg)
                        # 打印 AI 的思考回应
                        if msg.content:
                            logging.info(f"\n🔍 [主任务-AI思考/回应]: {msg.content}")

                        # 检查并以简洁格式打印工具调用
                        if hasattr(msg, 'tool_calls') and msg.tool_calls:
                            for tool_call in msg.tool_calls:
                                logging.info(f"🛠️  [主任务-工具调用]: 调用 `{tool_call['name']}`，参数: {tool_call['args']}")

            # 检查 'tools' key 并打印 ToolMessage
            if 'tools' in chunk and "messages" in chunk["tools"]:
                for output in chunk["tools"]["messages"]:
                    if isinstance(output, ToolMessage):
                        logging.info(f"\n✅ [主任务-工具输出]:\n{output.content}")



        
        # --- 主任务成功交互后，也存入历史 ---
        await history_manager.add_history(main_interaction_history)
        logging.info("✅ 主任务交互已成功存入向量化历史记录。")

    except Exception as e:
        logging.error(f"\n❌ 执行任务时发生严重错误: {e}") # 使用logging.error
        traceback.print_exc()
    finally:
        logging.info("\n--- 流程执行完毕，正在清理所有异步资源 ---")
        await cleanup_mcp_sessions()
        logging.info(token_tracker.get_summary())
        logging.info("\n--- 所有资源已成功清理 ---")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断。")